<template>
  <div class="personalProfileBox">
    <div class="pp_buttonList">
      <a-button v-if="btnPermission[720001]" type="primary" ghost @click="openDawer(1)">修改公司基本信息</a-button>
      <a-button v-if="btnPermission[720002]" type="primary" ghost @click="openDawer(2)">修改联系人</a-button>
      <a-button v-if="btnPermission[720003]" type="primary" ghost @click="openDawer(3)">修改财务信息</a-button>
      <a-button v-if="btnPermission[720004]" type="primary" ghost @click="openDawer(4)">修改文件证书</a-button>
    </div>
    <div class="pp_tabList">
      <a-tabs v-model:activeKey="activeKey" @change="changetab">
        <a-tab-pane key="1" tab="公司信息"></a-tab-pane>
        <a-tab-pane key="2" tab="联系人"></a-tab-pane>
        <a-tab-pane key="3" tab="财务信息"></a-tab-pane>
        <a-tab-pane key="4" tab="文件证书"></a-tab-pane>
      </a-tabs>
    </div>
    <div class="pp_tabContent">
      <personalInfo ref="personalInfoRef"></personalInfo>
    </div>
    <personalInfoEdit ref="personalInfoEditRef" @closeDrawer="getData(true)"></personalInfoEdit>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { defEmptyStr, getCommonOption } from '@/utils/index'
import { usePermission } from '@/hook/usePermission'
import dayjs from 'dayjs'
import personalInfo from './components/personalInfo.vue'
import personalInfoEdit from './components/personalInfoEdit.vue'
import { GetSupplier, GetDeliveryRegionList, GetProductCategoryList } from '../../../../servers/companyInfo'

const { btnPermission } = usePermission()

const activeKey = ref('1')
const personalInfoRef = ref<any>(null)
const personalInfoEditRef = ref<any>(null)
const companytable = ref([
  {
    title: '基本信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '供应商编码',
        key: 'number',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '证件编号',
        key: 'credit_id',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '统一社会信用代码',
        key: 'credit_code',
        link: 'https://www.gsxt.gov.cn/index.html',
        linkbtn: '去查询',
        type: 'link',
        editwidth: 50,
        edittype: 'link',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '供应商名称',
        key: 'supplier_name',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '营业执照有效期',
        key: 'business_license_validity',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '公司类型',
        key: 'company_type',
        type: 'textoptions',
        editwidth: 50,
        edittype: 'textoptions',
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '成立日期',
        key: 'establishment_date',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '法人',
        key: 'legal_person_name',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '经营规模',
        key: 'business_scale',
        type: 'textoptions',
        editwidth: 50,
        edittype: 'textoptions',
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '证件类型',
        key: 'certificate_type',
        type: 'textoptions',
        editwidth: 50,
        edittype: 'textoptions',
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '营业执照',
        // key: 'valuelink',
        key: ['business_license_file_id'],
        img: [],
        linkbtn: '预览',
        type: 'linkkeyarr',
        isshow: false,
        editwidth: 50,
        edittype: 'linkkeyarr',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '证件号',
        key: 'certificate_number',
        type: 'text',
        editwidth: 50,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '法人身份证',
        key: ['id_card_front_file_id', 'id_card_back_file_id'],
        img: [],
        linkbtn: '预览',
        isshow: false,
        type: 'linkkeyarr',
        editwidth: 50,
        edittype: 'linkkeyarr',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '营业执照地址',
        key: 'business_license_address',
        type: 'text',
        editwidth: 100,
        edittype: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '办公地址',
        key: 'office_address_detail',
        type: 'text',
        editwidth: 100,
        edittype: 'text',
      },
    ],
  },
  {
    title: '公司扩展信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '供应商类型',
        key: 'supplier_type',
        type: 'textoptions',
        editwidth: 50,
        edittype: 'textoptions',
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '主营类目',
        key: 'main_categories',
        type: 'textmoreoptions',
        editwidth: 100,
        edittype: 'checkbox',
        ismust: true,
        options: [
          // { label: 'Apple', value: 1 },
          // { label: 'Pear', value: 2 },
          // { label: 'Orange', value: 3 },
          // { label: 'Apple', value: 4 },
          // { label: 'Pear', value: 5 },
          // { label: 'Orange', value: 6 },
          // { label: 'Apple', value: 7 },
          // { label: 'Pear', value: 8 },
          // { label: 'Orange', value: 9 },
        ],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '自营/合作工厂规模',
        key: 'factory_scale',
        type: 'textoptions',
        editwidth: 100,
        edittype: 'radio',
        ismust: true,
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '主营区域',
        key: 'main_regions',
        type: 'textmoreoptions',
        editwidth: 100,
        edittype: 'checkbox',
        ismust: true,
        options: [],
      },
      {
        width: 50,
        labelwidth: 150,
        label: '工厂人员规模',
        key: 'factory_employee_count',
        type: 'text',
        editwidth: 100,
        edittype: 'textnumber',
        max: 99999,
        suffix: '人',
        min: 0,
      },
      {
        width: 50,
        labelwidth: 150,
        label: 'SKU数量',
        key: 'sku_count',
        type: 'text',
        max: 999999,
        editwidth: 100,
        edittype: 'textnumber',
        suffix: '个',
        min: 0,
      },
      {
        width: 50,
        labelwidth: 150,
        label: '主营商品',
        key: 'main_products',
        type: 'text',
        editwidth: 100,
        edittype: 'textinput',
        ismust: true,
        maxlength: 50,
      },
      {
        width: 50,
        labelwidth: 150,
        label: '公司年销售额',
        key: 'annual_sales',
        type: 'text',
        editwidth: 100,
        edittype: 'textnumber',
        suffix: '万元',
        precision: 2,
        max: 9999999.99,
        min: 0,
      },
      {
        width: 50,
        labelwidth: 150,
        label: '主营商品价格区间',
        key: 'main_products_min_price',
        key2: 'main_products_max_price',
        type: 'textnumberarr',
        editwidth: 100,
        precision: 2,
        max: 9999999.99,
        edittype: 'textnumberarr',
        suffix: '元',
        suffix2: '元',
        ismust: true,
        min: 0,
      },
    ],
  },
])
const companytableInfo = ref({
  fileList: [],
})
const telephonetable = ref([
  {
    title: '联系人信息',
    tableItem: [
      {
        width: 100,
        editwidth: 100,
        key: 'srs_supplier_contact_infos',
        type: 'table',
        edittype: 'table',
        tableInfo: [
          {
            fieldkey: 'name',
            title: '联系人姓名',
            ismust: true,
            type: 'text',
            edittype: 'textinput',
            maxlength: 20,
          },
          {
            fieldkey: 'job',
            title: '职务',
            ismust: false,
            type: 'text',
            edittype: 'textinput',
            maxlength: 20,
          },
          {
            fieldkey: 'mobile_phone_number',
            title: '手机号',
            ismust: true,
            type: 'text',
            edittype: 'textinputphone',
            maxlength: 13,
          },
          {
            fieldkey: 'email',
            title: '邮箱',
            ismust: false,
            type: 'text',
            edittype: 'textinput',
            maxlength: 50,
          },
          {
            fieldkey: 'weixin_number',
            title: '微信',
            ismust: false,
            type: 'text',
            edittype: 'textinput',
            maxlength: 20,
          },
          {
            fieldkey: 'is_default',
            title: '默认联系人',
            ismust: true,
            type: 'textoptions',
            edittype: 'defaultcontact',
            options: [
              {
                value: 1,
                label: '是',
              },
              {
                value: 2,
                label: '否',
              },
            ],
          },
          {
            ishide: true,
            fieldkey: '',
            title: '操作',
            ismust: false,
            width: 100,
            edittype: 'edit',
          },
        ],
      },
    ],
  },
])

const financetable = ref([
  {
    title: '财务信息',
    tableItem: [
      {
        width: 33,
        labelwidth: 150,
        label: '结算方式',
        key: 'settlement_method',
        type: 'textoptions',
        edittype: 'textselect',
        ismust: true,
        editwidth: 30,
        options: [
          // { label: 'Apple', value: 1 },
          // { label: 'Pear', value: 2 },
          // { label: 'Orange', value: 3 },
          // { label: 'Apple', value: 4 },
          // { label: 'Pear', value: 5 },
          // { label: 'Orange', value: 6 },
          // { label: 'Apple', value: 7 },
          // { label: 'Pear', value: 8 },
          // { label: 'Orange', value: 9 },
        ],
      },
      {
        width: 33,
        labelwidth: 150,
        label: '发票类型',
        key: 'invoice_type',
        type: 'textoptions',
        edittype: 'textselect',
        ismust: true,
        editwidth: 30,
        options: [
          // { label: 'Apple', value: 1 },
          // { label: 'Pear', value: 2 },
          // { label: 'Orange', value: 3 },
          // { label: 'Apple', value: 4 },
          // { label: 'Pear', value: 5 },
          // { label: 'Orange', value: 6 },
          // { label: 'Apple', value: 7 },
          // { label: 'Pear', value: 8 },
          // { label: 'Orange', value: 9 },
        ],
      },
      {
        width: 33,
        labelwidth: 150,
        label: '默认税率',
        key: 'default_tax_rate',
        type: 'textoptions',
        edittype: 'textselect',
        ismust: true,
        editwidth: 30,
        options: [
          // { label: 'Apple', value: 1 },
          // { label: 'Pear', value: 2 },
          // { label: 'Orange', value: 3 },
          // { label: 'Apple', value: 4 },
          // { label: 'Pear', value: 5 },
          // { label: 'Orange', value: 6 },
          // { label: 'Apple', value: 7 },
          // { label: 'Pear', value: 8 },
          // { label: 'Orange', value: 9 },
        ],
      },
      {
        width: 100,
        key: 'srs_supplier_finance_infos',
        type: 'table',
        edittype: 'table',
        editwidth: 100,
        tableInfo: [
          {
            fieldkey: 'account_name',
            title: '账户名称',
            ismust: true,
            type: 'text',
            edittype: 'textinput',
          },
          {
            fieldkey: 'account_type',
            title: '账户类型',
            ismust: true,
            type: 'textoptions',
            edittype: 'textselect',
            options: [
              // {
              //   value: 1,
              //   label: '选择1',
              // },
              // {
              //   value: 2,
              //   label: '选择2',
              // },
            ],
          },
          {
            fieldkey: 'collection_card_number',
            title: '银行卡号',
            ismust: true,
            type: 'text',
            edittype: 'textinput',
            maxlength: 30,
          },
          {
            fieldkey: 'collection_bank',
            title: '开户行',
            ismust: true,
            type: 'text',
            edittype: 'textinput',
            maxlength: 30,
          },
          {
            fieldkey: 'collection_bank_branch',
            title: '支行',
            ismust: true,
            type: 'text',
            edittype: 'textinput',
            maxlength: 30,
          },
          {
            fieldkey: 'is_default',
            title: '是否默认',
            ismust: true,
            type: 'textoptions',
            edittype: 'defaultcontact',
            options: [
              {
                value: 1,
                label: '是',
              },
              {
                value: 2,
                label: '否',
              },
            ],
          },
          {
            ishide: true,
            fieldkey: '',
            title: '操作',
            ismust: false,
            width: 100,
            edittype: 'edit',
          },
        ],
      },
    ],
  },
])

const filetable = ref([
  {
    title: '文件证书',
    tableItem: [
      {
        width: 50,
        labelwidth: 70,
        label: '证书上传',
        key: 'fileList',
        ismust: false,
        editwidth: 100,
        edittype: 'uploadfile',
        ishide: true,
        maxcount: 10,
      },
      {
        width: 50,
        labelwidth: 0,
        label: '',
        editwidth: 100,
        edittype: 'richtxet',
        ishide: true,
        maxcount: 10,
        html: `温馨提示：<br/>
              1. 涉及护肤品商品生产，如：洗护用品，膏霜乳液制品，唇膏、唇彩、眉笔、唇线笔、发蜡，牙膏类商品，清洁类商品等，需上传《化妆品生产许可证》；<br/>
              2. 涉及食品生产，如：粮食加工品、油脂及其制品，调味品，肉制品，乳制品，饮料方便食品，冷冻饮品，速冻食品等，需上传《食品生产许可证》；<br/>

              3. 涉及药品生产，如：化学药剂，中药制剂，生物制品，原材料药及药用辅料，特殊管理药品等，需上传《药品生产许可证》；<br/>
              4. 涉及工业品生产，如：建筑用钢筋，水泥，广播电视传输设备，电线电缆，危险化学品，化肥等，需上传《工业产品生产许可证》；<br/>

              5. 涉及医疗器械相关生产，如：基础耗材，护理用品，诊断监护类，治疗康复类，手术耗材类，医用卫生材料等，需上传《医疗器械生产许可证》；`,
      },
      {
        width: 100,
        key: 'license_files',
        type: 'table',
        edittype: 'table',
        editwidth: 100,
        tableInfo: [
          {
            fieldkey: 'original_name',
            title: '文件',
            ismust: false,
            type: 'text',
          },
          {
            fieldkey: 'account_name',
            title: '上传人',
            ismust: false,
            type: 'text',
          },
          {
            fieldkey: 'create_at',
            title: '上传时间',
            ismust: false,
            type: 'text',
          },
          {
            fieldkey: 'id',
            title: '操作',
            ismust: false,
            type: 'control',
            edittype: 'control',
          },
        ],
      },
    ],
  },
])
// const router = useRouter()
// watch(
//   () => router.currentRoute.value.path,
//   (newValue) => {
//     console.log('router')
//   },
//   { immediate: true },
// )
onMounted(async () => {
  console.log('父组件')
  await getDropdownItemsData()
  await getData(true)
})

const getDropdownItemsData = async () => {
  const [companytype_list, businessscale_list, certificatetype_list, settlementmethod_list, invoicetype_list, defaulttaxrate_list, publicAccountTypeList, factoryscale_list, supplierType] =
    await getCommonOption([5, 10, 4, 1, 8, 6, 3, 7, 20])
  companytable.value.forEach((item) => {
    item.tableItem.forEach((titem) => {
      if ((titem as any).key == 'company_type') {
        ;(titem as any).options = companytype_list
      }
      if ((titem as any).key == 'supplier_type') {
        ;(titem as any).options = supplierType
      }
      if ((titem as any).key == 'business_scale') {
        ;(titem as any).options = businessscale_list
      }
      if ((titem as any).key == 'certificate_type') {
        ;(titem as any).options = certificatetype_list
      }

      if ((titem as any).key == 'factory_scale') {
        ;(titem as any).options = factoryscale_list
      }
    })
  })
  financetable.value.forEach((item) => {
    item.tableItem.forEach((titem) => {
      if ((titem as any).key == 'settlement_method') {
        ;(titem as any).options = settlementmethod_list
      }
      if ((titem as any).key == 'invoice_type') {
        ;(titem as any).options = invoicetype_list
      }
      if ((titem as any).key == 'default_tax_rate') {
        ;(titem as any).options = defaulttaxrate_list
      }
      if ((titem as any).key == 'srs_supplier_finance_infos') {
        ;(titem as any).tableInfo.forEach((ttitem) => {
          if (ttitem.fieldkey == 'account_type') {
            ;(ttitem as any).options = publicAccountTypeList
          }
        })
      }
    })
  })
  const params2 = { page: 1, pageSize: 9999 }
  const res2 = await GetDeliveryRegionList(params2)
  if (res2.success == true) {
    const arr: any = []
    res2.data.list.forEach((item) => {
      const obj = {
        value: item.id,
        label: item.name,
      }
      arr.push(obj)
    })
    companytable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if ((titem as any).key == 'main_regions') {
          // 主营区域：
          ;(titem as any).options = arr
        }
      })
    })
  } else {
    message.warning(res2.message)
  }
  const params3 = { page: 1, pageSize: 9999 }
  const res3 = await GetProductCategoryList(params3)
  if (res3.success == true) {
    const arr: any = []
    res3.data.list.forEach((item) => {
      const obj = {
        value: item.id,
        label: item.name,
      }
      arr.push(obj)
    })
    companytable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if ((titem as any).key == 'main_categories') {
          // 主营类目：
          ;(titem as any).options = arr
        }
      })
    })
  } else {
    // message.warning('接口报错')
    message.warning(res3.message)
  }
  console.log(' companytable.value', companytable.value)
  console.log('financetable.value', financetable.value)
}
const getData = async (bol) => {
  const params = {
    preview: bol, // 查看true,修改false
  }
  const res = await GetSupplier(params)
  if (res.success == true) {
    res.data.business_license_address = res.data.business_license_province + res.data.business_license_city + res.data.business_license_area + res.data.business_license_address
    res.data.office_address_detail =
      defEmptyStr(res.data.office_address_province) + defEmptyStr(res.data.office_address_city) + defEmptyStr(res.data.office_address_area) + defEmptyStr(res.data.office_address_detail)
    res.data.srs_supplier_contact_infos.forEach((item) => {
      item.is_default = item.is_default == true ? 1 : 2
      // 1 是，2否
    })
    res.data.srs_supplier_finance_infos.forEach((item) => {
      item.is_default = item.is_default == true ? 1 : 2
    })
    res.data.old_main_categories = res.data.main_categories
    // 检查文件证明是否上传文件应该为必填
    filetable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if (titem.key == 'fileList') {
          if (res.data.license_files.length == 0) {
            titem.ismust = true
          } else {
            titem.ismust = false
          }
        }
      })
    })
    if (res.data.settlement_method == 0) {
      res.data.settlement_method = null
    }
    if (res.data.establishment_date) res.data.establishment_date = dayjs(res.data.establishment_date).format('YYYY-MM-DD')

    companytableInfo.value = res.data
    gettabledata()
  } else {
    // message.warning('接口报错')
    message.warning(res.message)
  }
}
const gettabledata = () => {
  console.log('gettabledata', personalInfoRef.value)

  if (activeKey.value == '1' && personalInfoRef.value != null) {
    personalInfoRef.value.init(companytable.value, companytableInfo.value)
  }
  if (activeKey.value == '2' && personalInfoRef.value != null) {
    personalInfoRef.value.init(telephonetable.value, companytableInfo.value)
  }
  if (activeKey.value == '3' && personalInfoRef.value != null) {
    personalInfoRef.value.init(financetable.value, companytableInfo.value)
  }
  if (activeKey.value == '4' && personalInfoRef.value != null) {
    personalInfoRef.value.init(filetable.value, companytableInfo.value)
  }
}
const openDawer = async (type) => {
  await getData(false)
  if (type == '1') {
    personalInfoEditRef.value.init(companytable.value, companytableInfo.value, '编辑公司信息', null, type)
  }
  if (type == '2') {
    personalInfoEditRef.value.init(telephonetable.value, companytableInfo.value, '编辑联系人信息', null, type)
  }
  if (type == '3') {
    personalInfoEditRef.value.init(financetable.value, companytableInfo.value, '编辑财务信息', null, type)
  }
  if (type == '4') {
    personalInfoEditRef.value.init(filetable.value, companytableInfo.value, '编辑文件证书', 'license_files', type)
  }
}
const changetab = () => {
  console.log('activeKey', activeKey.value)
  gettabledata()
}
</script>

<style lang="scss" scoped>
.personalProfileBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  color: #000;

  .pp_buttonList {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    .ant-btn {
      margin-left: 10px;
    }
  }

  .pp_tabList {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    width: 100%;

    .ant-tabs {
      width: 100%;
    }
  }

  .pp_tabContent {
    width: 100%;
  }
}
</style>
