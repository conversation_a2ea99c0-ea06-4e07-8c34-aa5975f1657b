<template>
  <div class="h-full flex-shrink-0 base-image relative">
    <loading-outlined v-if="loading" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 c-primary" />
    <img v-else class="h-full w-full" :src="previewUrl || src || ErrorIcon" :preview="!!src || !!previewUrl" loading="lazy" />
    <div class="base-image-mask" v-if="!!src || !!previewUrl" @click="() => setVisible(true)">
      <EyeOutlined class="text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
    </div>
    <a-image
      v-if="!!src || !!previewUrl"
      :width="200"
      :style="{ display: 'none' }"
      :preview="{
        visible,
        onVisibleChange: setVisible,
      }"
      :src="previewUrl || src"
    />
  </div>
</template>
<script setup lang="ts">
import { EyeOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import ErrorIcon from '@/assets/icons/error-image.svg'
import { getPreviewUrl } from '@/utils/index'

const props = withDefaults(
  defineProps<{
    src: string | null
    height?: number | string
    id?: number | null
  }>(),
  {
    src: '',
    height: '100%',
  },
)

const visible = ref<boolean>(false)
const setVisible = (value): void => {
  visible.value = value
}

const loading = ref(false)

const previewUrl = ref()

// 获取附件链接
const getFileUrl = async () => {
  if (!props.id) {
    previewUrl.value = null
    return
  }
  try {
    loading.value = true
    const newUrl = await getPreviewUrl(props.id)
    previewUrl.value = newUrl
  } finally {
    loading.value = false
  }
}

watch(
  () => props.id,
  () => {
    getFileUrl()
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.base-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  z-index: 1;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  &:hover {
    opacity: 1;
  }
}
</style>
