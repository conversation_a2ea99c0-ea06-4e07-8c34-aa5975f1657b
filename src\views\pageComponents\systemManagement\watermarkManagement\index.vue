<template>
  <div>
    <div class="breadcrumb-box">
      <div class="title">全局水印</div>
    </div>
    <div class="card-box">
      <div class="card">
        <div class="title">操作界面水印</div>
        <a-button :disabled="!btnPermission[32001]" @click="handleShowSetting">设置</a-button>
        <a-button :disabled="!btnPermission[32002]" @click="handleShowLog(1)">日志</a-button>
        <div class="description">配置后将生效于所有操作界面。</div>
      </div>
    </div>
    <WatermarkSettingDrawer :rolesoptions="rolesOptions" ref="settingRef" />
    <WatermarkLogDrawer ref="logRef" />
  </div>
</template>

<script setup lang="ts">
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { checkPagePermission } from '@/utils'
import WatermarkLogDrawer from './components/WatermarkLogDrawer.vue'
import WatermarkSettingDrawer from './components/WatermarkSettingDrawer.vue'

const rolesOptions = ref([])
const settingRef = useTemplateRef<InstanceType<typeof WatermarkSettingDrawer>>('settingRef')
const logRef = useTemplateRef<InstanceType<typeof WatermarkLogDrawer>>('logRef')

const { btnPermission } = usePermission()

// 显示日志
const handleShowLog = (page) => {
  logRef.value?.open(page)
}
// 显示设置
const handleShowSetting = () => {
  settingRef.value?.open()
}

onMounted(() => {
  // 检查当前用户是否有权限访问水印管理页面
  const hasWatermarkManagementPermission = checkPagePermission('/watermarkManagement')

  if (hasWatermarkManagementPermission) {
    GetRoleSelectOption({ page: 1, pageSize: 100 }).then((res) => {
      console.log('获取角色列表：', res)
      res.data.forEach((x) => {
        x.label = x.role_name
        x.value = x.role_id
      })
      rolesOptions.value = res.data
    })
  } else {
    console.warn('用户无权限访问水印管理页面，跳过接口调用')
  }
})
</script>

<style lang="scss" scoped>
.breadcrumb-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  margin-bottom: 8px;

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
}

.card-box {
  border: 1px solid #dcdcdc;
  border-radius: 4px;

  .card {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 24px;
    font-size: 12px;
    color: #000;

    .title {
      margin-right: 40px;
    }

    .description {
      margin-left: 10px;
      color: rgb(0 0 0 / 50%);
    }
  }
}
</style>
