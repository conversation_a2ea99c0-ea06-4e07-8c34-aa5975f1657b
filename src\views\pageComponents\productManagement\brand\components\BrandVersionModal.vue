<template>
  <!-- 版本记录弹窗 -->
  <a-modal v-model:open="visible" title="版本列表" :width="1000" :footer="null" @cancel="handleClose">
    <a-spin :spinning="loading">
      <a-table :data-source="versionList" :pagination="false" :scroll="{ y: 400 }" row-key="auth_version" @change="handleTableChange">
        <a-table-column title="版本号" data-index="auth_version" :width="100" />
        <a-table-column title="品牌授权书(中文)" data-index="original_name" :width="150">
          <template #default="{ record }">
            <span class="auth-file-link" @click="viewFile(record.file_id)">{{ record.original_name || record.name }}</span>
          </template>
        </a-table-column>
        <a-table-column title="品牌授权书(英文)" data-index="original_name" :width="150">
          <template #default="{ record }">
            <span class="auth-file-link" @click="viewFile(record.file_id_en)">
              {{ record.original_name_en }}
            </span>
          </template>
        </a-table-column>

        <a-table-column title="授权期限" :width="200">
          <template #default="{ record }">
            <span v-if="record.auth_start_at && record.auth_end_at">{{ dayjs(record.auth_start_at).format('YYYY-MM-DD') }} 至 {{ dayjs(record.auth_end_at).format('YYYY-MM-DD') }}</span>
            <span v-else>--</span>
          </template>
        </a-table-column>
        <a-table-column
          title="保存时间"
          data-index="modified_at"
          :width="150"
          :sortable="true"
          :sorter="
            (a, b) => {
              const dateA = a.modified_at ? new Date(a.modified_at).getTime() : 0
              const dateB = b.modified_at ? new Date(b.modified_at).getTime() : 0
              return dateA - dateB
            }
          "
          :default-sort-order="'descend'"
        >
          <template #default="{ record }">
            {{ record.modified_at ? dayjs(record.modified_at).format('YYYY-MM-DD HH:mm') : '--' }}
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="80">
          <template #default="{ record }">
            <a-space>
              <!-- <a-button type="link" size="small" @click="viewFile(record.file_id)">
                查看
              </a-button> -->
              <a-button type="link" size="small" @click="downloadFile(record.brand_id, record.auth_version)">下载</a-button>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { GetAuthFiles } from '@/servers/Brand'
import dayjs from 'dayjs'

// 组件状态
const visible = ref(false)
const loading = ref(false)
const versionList = ref<any[]>([])
const currentBrandId = ref<number | null>(null)

// 显示版本记录弹窗
const show = async (brandId: number) => {
  if (!brandId) {
    message.warning('品牌ID不存在')
    return
  }

  currentBrandId.value = brandId
  visible.value = true
  loading.value = true

  try {
    const res = await GetAuthFiles({ id: brandId })
    const list = res.data || []

    // 默认按保存时间降序排列（最新的在前面）
    list.sort((a: any, b: any) => {
      const dateA = a.modified_at ? new Date(a.modified_at).getTime() : 0
      const dateB = b.modified_at ? new Date(b.modified_at).getTime() : 0
      return dateB - dateA // 降序排列
    })

    versionList.value = list
  } catch (error) {
    console.error('获取版本记录失败:', error)
    message.error('获取版本记录失败')
    versionList.value = []
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  versionList.value = []
  currentBrandId.value = null
}

// 处理表格变化（排序等）
const handleTableChange = (_pagination: any, _filters: any, sorter: any) => {
  console.log('版本列表排序变化:', sorter)
  // 这里可以添加额外的排序逻辑，如果需要的话
}

// 查看文件
const viewFile = async (fileId: number) => {
  if (!fileId) {
    message.warning('文件不存在')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const url = `${baseUrl}?fileId=${fileId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      message.warning('获取文件失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
  } catch (error) {
    console.error('文件预览失败:', error)
    message.error('文件预览失败')
  }
}

// 下载文件
const downloadFile = async (brandId: number, auth_version: number, originalFileName?: string) => {
  if (!brandId) {
    message.warning('缺少品牌ID')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/BrandInfo/DownloadAuthFilesZip' : '/api/BrandInfo/DownloadAuthFilesZip'
    // const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/DownloadCenter/Add' : '/api/DownloadCenter/Add'

    // 准备请求体参数
    const requestBody = JSON.stringify({
      id: brandId,
      version: auth_version,
      // export_type_identifier: 3,
      // data_source: 'SRS',
    })

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        logintoken: loginToken,
        'Content-Type': 'application/json', // 设置JSON格式的请求头
      },
      body: requestBody, // 将参数放在请求体中
    })

    if (!response.ok) {
      console.error('下载请求失败:', response.status)
      message.error('文件下载失败')
      return
    }

    // 关键：获取文件blob并创建下载链接
    const blob = await response.blob()
    const urlObject = URL.createObjectURL(blob)

    // 创建a标签触发下载
    const a = document.createElement('a')
    a.href = urlObject

    // 优先从响应头获取文件名（支持中文）
    const contentDisposition = response.headers.get('content-disposition')
    let fileName = originalFileName || '品牌授权书压缩包.zip' // 默认文件名
    if (contentDisposition) {
      // 解析content-disposition中的文件名（处理UTF-8编码）
      const match = contentDisposition.match(/filename\*=UTF-8''(.*)/)
      if (match && match[1]) {
        fileName = decodeURIComponent(match[1]) // 解码UTF-8文件名
      }
    }
    a.download = fileName // 设置下载文件名

    // 触发点击并清理资源
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(urlObject) // 释放临时URL

    message.success('文件下载成功')
  } catch (error) {
    console.error('文件下载失败:', error)
    message.error('文件下载失败')
  }
}

// 暴露方法给父组件
defineExpose({
  show,
})
</script>

<style scoped>
.auth-file-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }

  &:active {
    color: #096dd9;
  }
}
/* 可以添加一些样式 */
</style>
