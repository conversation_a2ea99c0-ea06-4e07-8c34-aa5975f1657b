<template>
  <a-drawer :footerStyle="{ paddingLeft: '24px' }" destroy-on-close @close="onClose()" v-model:open="drawerVisible" width="800px" title="审批设置" placement="right" :maskClosable="false">
    <div class="dragMainBox">
      <a-spin v-show="drawerLoading" />
      <a-form :colon="false" :label-col="{ style: { width: '110px', marginRight: '20px' } }" v-show="!drawerLoading" ref="formRef" :model="formData" :rules="rules">
        <p>配置流程</p>
        <a-form-item label="配置流程">
          <a-select
            v-model:value="formData.flow_type"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
            @change="changeSettingType"
          >
            <a-select-option v-for="item in flowTypeOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <template v-if="formData.flow_type === 1">
          <a-form-item label="审批流程" name="approval">
            <a-table class="table" :dataSource="formData?.approve" :columns="columns" :pagination="false" bordered size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
                <template v-if="column.dataIndex === 'approval'">
                  <a-form-item-rest>
                    <a-space>
                      <a-select
                        v-model:value="record.role_id"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        show-search
                        :filter-option="(input, option) => filterOption(input, option)"
                        class="short"
                        placeholder="请选择角色"
                        @change="changeApproveRole($event, index)"
                      >
                        <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                      </a-select>
                      <a-select
                        v-model:value="record.user_id"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        show-search
                        :filter-option="(input, option) => filterOption(input, option)"
                        class="short"
                        placeholder="请选择负责人"
                        @change="formRef.validateFields('approval')"
                        @focus="focusApprovalUser(index)"
                      >
                        <a-select-option v-for="(item, i) in approvalUserOptions[`approvalUserOptions${index}`]" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                      </a-select>
                    </a-space>
                  </a-form-item-rest>
                  <a-space class="icon-btn-group">
                    <PlusCircleFilled class="icon blue add-icon" @click="onAdd('approval', index)" />
                    <MinusCircleFilled class="icon red del-icon" v-if="index > 0" @click="onDelete('approval', index)" />
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-form-item>
          <a-form-item label="运维主管" name="operations_supervisor">
            <a-space>
              <a-form-item-rest>
                <a-select
                  v-model:value="formData.operations_supervisor_role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeSupervisorRole"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item-rest>
              <a-select
                v-model:value="formData.operations_supervisor"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                class="short"
                placeholder="请选择负责人"
                @focus="focusSupervisorUser()"
              >
                <a-select-option v-for="item in supervisorOptions" :value="item.value" :key="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-space>
          </a-form-item>
          <a-form-item label="抄送人">
            <div v-for="(item, index) in formData?.carbon_copy_recipients" :key="index" class="copy-send-item">
              <a-space>
                <a-select
                  v-model:value="item.role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeCopySendRole($event, index)"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
                <a-select
                  v-model:value="item.user_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择负责人"
                  @focus="focusCopySendUser(index)"
                >
                  <a-select-option v-for="(item, i) in copySendUserOptions[`copySendUserOptions${index}`]" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
                <a-space class="copy-icon-group">
                  <PlusCircleFilled class="icon blue" @click="onAdd('copySend', index)" />
                  <MinusCircleFilled class="icon red" @click="onDelete('copySend', index)" />
                </a-space>
              </a-space>
            </div>
            <PlusCircleFilled v-if="!formData?.carbon_copy_recipients.length" class="icon blue" @click="onAdd('copySend', 0)" />
          </a-form-item>
        </template>
        <template v-if="formData.flow_type === 2">
          <a-form-item label="审批流程" name="approval">
            <a-table class="table" :dataSource="formData?.approve" :columns="columns" :pagination="false" bordered size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
                <template v-if="column.dataIndex === 'approval'">
                  <a-form-item-rest>
                    <a-space>
                      <a-select
                        v-model:value="record.role_id"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        show-search
                        :filter-option="(input, option) => filterOption(input, option)"
                        class="short"
                        placeholder="请选择角色"
                        @change="changeApproveRole($event, index)"
                      >
                        <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                      </a-select>
                      <a-select
                        v-model:value="record.user_id"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        show-search
                        :filter-option="(input, option) => filterOption(input, option)"
                        class="short"
                        placeholder="请选择负责人"
                        @focus="focusApprovalUser(index)"
                      >
                        <a-select-option v-for="(item, i) in approvalUserOptions[`approvalUserOptions${index}`]" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                      </a-select>
                    </a-space>
                  </a-form-item-rest>
                </template>
                <template v-if="column.dataIndex === 'license'">
                  <a-form-item-rest>
                    <a-checkbox v-model:checked="record.is_business_license" @change="changeCheckbox($event, index)"></a-checkbox>
                  </a-form-item-rest>
                  <a-space class="icon-btn-group">
                    <PlusCircleFilled class="icon blue add-icon" @click="onAdd('approval', index)" />
                    <MinusCircleFilled class="icon red del-icon" v-if="index > 0" @click="onDelete('approval', index)" />
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-form-item>
          <a-form-item label="信息主管" name="information_supervisor">
            <a-space>
              <a-form-item-rest>
                <a-select
                  v-model:value="formData.information_supervisor_role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeInformationSupervisorRole"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item-rest>
              <a-select
                v-model:value="formData.information_supervisor"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                class="short"
                placeholder="请选择负责人"
                @focus="focusInformationSupervisorUser"
              >
                <a-select-option v-for="item in informationUserOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-space>
          </a-form-item>
          <a-form-item label="财务主管" name="financial_supervisor">
            <a-space>
              <a-form-item-rest>
                <a-select
                  v-model:value="formData.financial_supervisor_role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeFinancialSupervisorRole"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item-rest>
              <a-select
                v-model:value="formData.financial_supervisor"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                class="short"
                placeholder="请选择负责人"
                @focus="focusFinancialSupervisorUser"
              >
                <a-select-option v-for="item in financialUserOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-space>
          </a-form-item>
          <a-form-item label="出纳" name="cashier">
            <a-space>
              <a-form-item-rest>
                <a-select
                  v-model:value="formData.cashier_role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeCashierRole"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item-rest>
              <a-select
                v-model:value="formData.cashier"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                class="short"
                placeholder="请选择负责人"
                @focus="focusCashierUser"
              >
                <a-select-option v-for="item in cashierUserOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-space>
          </a-form-item>
          <a-form-item label="运维主管" name="operations_supervisor">
            <a-space>
              <a-form-item-rest>
                <a-select
                  v-model:value="formData.operations_supervisor_role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeSupervisorRole"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item-rest>
              <a-select
                v-model:value="formData.operations_supervisor"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                class="short"
                placeholder="请选择负责人"
                @focus="focusSupervisorUser()"
              >
                <a-select-option v-for="item in supervisorOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-space>
          </a-form-item>
          <a-form-item label="抄送人">
            <div v-for="(item, index) in formData?.carbon_copy_recipients" :key="index" class="copy-send-item">
              <a-space>
                <a-select
                  v-model:value="item.role_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择角色"
                  @change="changeCopySendRole($event, index)"
                >
                  <a-select-option v-for="(item, i) in roleOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
                <a-select
                  v-model:value="item.user_id"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  class="short"
                  placeholder="请选择负责人"
                  @focus="focusCopySendUser(index)"
                >
                  <a-select-option v-for="(item, i) in copySendUserOptions[`copySendUserOptions${index}`]" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
                </a-select>
                <a-space class="copy-icon-group">
                  <PlusCircleFilled class="icon blue" @click="onAdd('copySend', index)" />
                  <MinusCircleFilled class="icon red" @click="onDelete('copySend', index)" />
                </a-space>
              </a-space>
            </div>
            <PlusCircleFilled v-if="!formData?.carbon_copy_recipients.length" class="icon blue" @click="onAdd('copySend', 0)" />
          </a-form-item>
        </template>
      </a-form>
    </div>

    <template #footer>
      <a-button :loading="submitLoading || drawerLoading" style="margin-right: 10px" @click="submit" type="primary">保存</a-button>
      <a-button
        @click="
          () => {
            onClose()
          }
        "
      >
        取消
      </a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { filterOption } from '@/utils/index'
import { PlusCircleFilled, MinusCircleFilled } from '@ant-design/icons-vue'
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { GetUserOptions } from '@/servers/UserManager'
import { GetEnum } from '@/servers/Common'
import { GetApprovalSetting, SaveApprovalSetting } from '@/servers/Approval'
import { message } from 'ant-design-vue'

const formRef = ref<any>(null)
const drawerLoading = ref(false)
const submitLoading = ref(false)
const drawerVisible = ref(false)
const formData = ref<any>({
  flow_type: 1,
  carbon_copy_recipients: [],
  operations_supervisor: null,
})

const rules1 = {
  approval: [
    {
      required: true,
      validator: () => {
        if (!formData.value?.approve.find((n) => n.user_id && n.role_id)) {
          return Promise.reject('请选择审批流程')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
  operations_supervisor: [
    {
      required: true,
      validator: () => {
        if (!formData.value.operations_supervisor_role_id || !formData.value.operations_supervisor) {
          return Promise.reject('请选择运维主管')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
}
const rules2 = {
  approval: [
    {
      required: true,
      validator: () => {
        if (!formData.value?.approve.find((n) => n.user_id && n.role_id)) {
          return Promise.reject('请选择审批流程')
        }
        if (!formData.value?.approve.find((n) => n.is_business_license)) {
          return Promise.reject('请选择指定执照')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
  operations_supervisor: [
    {
      required: true,
      validator: () => {
        if (!formData.value.operations_supervisor_role_id || !formData.value.operations_supervisor) {
          return Promise.reject('请选择运维主管')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
  cashier: [
    {
      required: true,
      validator: () => {
        if (!formData.value.cashier_role_id || !formData.value.cashier) {
          return Promise.reject('请选择出纳')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
  financial_supervisor: [
    {
      required: true,
      validator: () => {
        if (!formData.value.financial_supervisor_role_id || !formData.value.financial_supervisor) {
          return Promise.reject('请选择财务主管')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
  information_supervisor: [
    {
      required: true,
      validator: () => {
        if (!formData.value.information_supervisor_role_id || !formData.value.information_supervisor) {
          return Promise.reject('请选择信息主管')
        }
        return Promise.resolve()
      },
      trigger: ['change', 'blur'],
    },
  ],
}

const rules = ref(rules1)

const flowTypeOptions = ref<any>([])
const roleOptions = ref<any>([])
const approvalUserOptions = reactive<any>({
  approvalUserOptions0: [],
})
const copySendUserOptions = reactive<any>({
  copySendUserOptions0: [],
})
const supervisorOptions = ref<any>([]) // 运维主管负责人下拉列表
const informationUserOptions = ref<any>([]) // 信息主管负责人下拉列表
const financialUserOptions = ref<any>([]) // 财务主管负责人下拉列表
const cashierUserOptions = ref<any>([]) // 出纳负责人下拉列表

const changeColumns = [
  {
    title: '审批层级',
    dataIndex: 'index',
    width: 70,
    align: 'center',
  },
  {
    title: '审批人',
    dataIndex: 'approval',
    align: 'center',
  },
]
const columns = ref(changeColumns)

const registColumns = [
  {
    title: '审批层级',
    dataIndex: 'index',
    width: 70,
    align: 'center',
  },
  {
    title: '审批人',
    dataIndex: 'approval',
    align: 'center',
  },
  {
    title: '指定执照',
    dataIndex: 'license',
    width: 70,
    align: 'center',
  },
]

const changeSettingType = () => {
  if (formData.value.flow_type === 1) {
    columns.value = changeColumns
    rules.value = rules1
    approvalUserOptions.approvalUserOptions0 = []
  } else if (formData.value.flow_type === 2) {
    columns.value = registColumns
    rules.value = rules2
    copySendUserOptions.copySendUserOptions0 = []
  }
  formData.value = {
    flow_type: formData.value.flow_type,
    carbon_copy_recipients: [],
    operations_supervisor: null,
  }
  getSetting()
}

const onAdd = (type, index) => {
  if (type === 'approval') {
    formData.value?.approve.splice(index + 1, 0, {
      user_id: null,
      role_id: null,
    })
    approvalUserOptions[`approvalUserOptions${index + 1}`] = []
  } else if (type === 'copySend') {
    formData.value?.carbon_copy_recipients.splice(index + 1, 0, {
      user_id: null,
      role_id: null,
    })
    copySendUserOptions[`copySendUserOptions${index + 1}`] = []
  }
}

const onDelete = (type, index) => {
  if (type === 'approval') {
    formData.value.approve.splice(index, 1)
  } else if (type === 'copySend') {
    formData.value.carbon_copy_recipients.splice(index, 1)
  }
}

const changeApproveRole = (_val, index) => {
  approvalUserOptions[`approvalUserOptions${index}`] = []
  formData.value.approve[index].user_id = null
}

const focusApprovalUser = (index) => {
  const val = formData.value.approve[index].role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      approvalUserOptions[`approvalUserOptions${index}`] = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
    })
  } else {
    approvalUserOptions[`approvalUserOptions${index}`] = []
  }
  formRef.value.validateFields('approval')
}

const changeSupervisorRole = () => {
  supervisorOptions.value = []
  formData.value.operations_supervisor = null
}

const focusSupervisorUser = () => {
  const val = formData.value.operations_supervisor_role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      supervisorOptions.value = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
    })
  } else {
    supervisorOptions.value = []
  }
  formRef.value.validateFields('approval')
}

const changeCopySendRole = (_val, index) => {
  copySendUserOptions[`copySendUserOptions${index}`] = []
  formData.value.carbon_copy_recipients[index].user_id = null
}

const focusCopySendUser = (index) => {
  const val = formData.value.carbon_copy_recipients[index].role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      copySendUserOptions[`copySendUserOptions${index}`] = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
      console.log('copySendUserOptions', copySendUserOptions.value)
    })
  } else {
    copySendUserOptions[`copySendUserOptions${index}`] = []
  }
}

const changeInformationSupervisorRole = () => {
  informationUserOptions.value = []
  formData.value.information_supervisor = null
}
const focusInformationSupervisorUser = () => {
  const val = formData.value.information_supervisor_role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      informationUserOptions.value = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
    })
  } else {
    informationUserOptions.value = []
  }
  formRef.value.validateFields('information_supervisor')
}

const changeFinancialSupervisorRole = () => {
  financialUserOptions.value = []
  formData.value.financial_supervisor = null
}
const focusFinancialSupervisorUser = () => {
  const val = formData.value.financial_supervisor_role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      financialUserOptions.value = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
    })
  } else {
    financialUserOptions.value = []
  }
  formRef.value.validateFields('financial_supervisor')
}

const changeCashierRole = () => {
  cashierUserOptions.value = []
  formData.value.cashier = null
}
const focusCashierUser = () => {
  const val = formData.value.cashier_role_id
  if (val) {
    GetUserOptions({ roleid: val }).then((res) => {
      cashierUserOptions.value = res.data.map((x) => {
        return { label: x.principal, value: x.id }
      })
    })
  } else {
    cashierUserOptions.value = []
  }
  formRef.value.validateFields('cashier')
}

const submit = async () => {
  try {
    await formRef.value.validateFields()
    submitLoading.value = true
    formData.value.approve = formData.value.approve.filter((n) => n.user_id && n.role_id)
    SaveApprovalSetting(formData.value)
      .then(() => {
        message.success('保存成功')
        onClose()
        submitLoading.value = false
      })
      .catch(() => {
        submitLoading.value = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
    submitLoading.value = false
  }
}

const changeCheckbox = (e, index) => {
  if (e?.target?.checked) {
    formData.value?.approve.forEach((item, idx) => {
      if (idx != index) {
        item.is_business_license = false
      }
    })
  }
}

const onClose = () => {
  drawerVisible.value = false
  formRef.value.resetFields()
  formData.value.flow_type = 1
  columns.value = changeColumns
  rules.value = rules1
  approvalUserOptions.approvalUserOptions0 = []
}

const getOptions = () => {
  GetRoleSelectOption(null).then((res) => {
    roleOptions.value = res.data.map((x) => {
      return { label: x.role_name, value: `${x.role_id}` }
    })
  })
  GetEnum().then((res) => {
    flowTypeOptions.value = res.data.approval.configuration_flow
  })
}

const getSetting = () => {
  GetApprovalSetting({ flow_type: formData.value.flow_type }).then((res) => {
    formData.value = res.data
    if (!formData.value.approve.length) {
      onAdd('approval', 0)
    }
    formData.value.approve.forEach((item, i) => {
      approvalUserOptions[`approvalUserOptions${i}`] = [{ value: item.user_id, label: item.user_name }]
    })
    if (formData.value.operations_supervisor) {
      supervisorOptions.value = [{ value: formData.value.operations_supervisor, label: formData.value.operations_supervisor_user_name }]
    }
    if (formData.value.carbon_copy_recipients) {
      formData.value.carbon_copy_recipients.forEach((item, i) => {
        copySendUserOptions[`copySendUserOptions${i}`] = [{ value: item.user_id, label: item.user_name }]
      })
    }
    if (formData.value.information_supervisor) {
      informationUserOptions.value = [{ value: formData.value.information_supervisor, label: formData.value.information_supervisor_user_name }]
    }
    if (formData.value.financial_supervisor) {
      financialUserOptions.value = [{ value: formData.value.financial_supervisor, label: formData.value.financial_supervisor_user_name }]
    }
    if (formData.value.cashier) {
      cashierUserOptions.value = [{ value: formData.value.cashier, label: formData.value.cashier_user_name }]
    }
    formData.value.operations_supervisor_role_id = formData.value.operations_supervisor_role_id || null
    formData.value.operations_supervisor = formData.value.operations_supervisor || null
  })
}

const open = () => {
  //   drawerLoading.value = true
  drawerVisible.value = true
  getOptions()
  getSetting()
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.short {
  width: 16.25rem;
}

.table {
  width: 90%;
}

.icon-btn-group {
  text-align: left;
}

.icon {
  font-size: 16px;
}

.add-icon {
  position: absolute;
  right: -25px;
  margin-top: -12px;
}

.del-icon {
  position: absolute;
  right: -50px;
  margin-top: -12px;
}

.blue {
  color: #409eff;
}

.red {
  color: #f81d22;
}

.copy-icon-group {
  position: absolute;
  left: 410px;
  margin-top: -10px;
}

.copy-send-item {
  margin-bottom: 10px;
}
</style>
