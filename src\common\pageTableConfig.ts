import { defaultFormatter, priceFormatter } from '@/utils/VxeUi'
import { PageType } from './enum'

export const pageTableConfig = {
  [PageType.ROLE_MANAGE]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'role_name', name: '角色名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'creater', name: '创建人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'modifier', name: '更新人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'update_at', name: '更新时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 100, freeze: 0, is_show: true, is_sort: false, align: 'center' },
    { key: 'operate', name: '操作', width: 130, freeze: 2, is_show: true },
  ],

  [PageType.USER_MANAGE]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'uid', name: '账号', freeze: 0, is_show: true, is_sort: true },
    { key: 'job_id', name: '工号', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'real_name', name: '姓名', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'department_name', name: '部门', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'position_name', name: '岗位', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'leader_name', name: '直接上级', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'role_names', name: '角色', width: 120, freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', width: 72, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最近修改时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 130, freeze: 2, is_show: true },
  ],
  [PageType.BRAND_MANAGEMENT]: [
    { key: 'brand_number', name: '品牌编码', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'brand_name', name: '品牌名称', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'logo_url', name: 'LOGO', width: 80, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'image', props: { fileIdKey: 'logo_id' } } },
    { key: 'manufacturer_name', name: '默认制造商', width: 100, freeze: 0, is_show: true, is_sort: false },

    { key: 'auth_file_original_name', name: '品牌授权书(中文)', width: 180, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_file_en_original_name', name: '品牌授权书(英文)', width: 180, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_period', name: '授权期限', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_status', name: '授权书状态', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_version', name: '授权书版本号', width: 80, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 100, freeze: 0, is_show: true, is_sort: true },

    { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 159, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 160, freeze: 2, is_show: true },
  ],

  [PageType.NOTICE_MANAGE]: [
    // { key: 'code', name: '通知编码', width: 130, freeze: 0, is_show: true, is_must: false, index: 10 },
    { key: 'title', name: '通知标题', width: 320, freeze: 0, is_show: true, index: 20, is_sort: false },
    { key: 'content', name: '通知内容', width: 1000, freeze: 0, is_show: true, index: 30, is_sort: false },
    // { key: 'scope', name: '通知范围', width: 150, freeze: 0, is_show: true, is_sort: true, index: 40 },
    // { key: 'scheduled_publish_at', name: '计划发布时间', width: 160, freeze: 0, is_show: true, is_sort: true, index: 50 },
    // { key: 'notice_status', name: '状态', width: 105, freeze: 0, is_show: true, is_sort: false, index: 60 },
    { key: 'publish_at', name: '发布时间', width: 260, freeze: 0, is_show: true, is_sort: true, index: 70 },
    // { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true, index: 80 },
    // { key: 'update_at', name: '最后修改时间', width: 150, freeze: 0, is_show: true, is_sort: true, index: 90 },
    { key: 'operate', name: '操作', width: 100, freeze: 2, is_show: true, is_must: true, index: 91 },
  ],
  [PageType.PRODUCT_LIBRARY]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'main_image', name: '商品主图', width: 94, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'image', props: { fileIdKey: 'main_images_id' } } },
    { key: 'supplier_product_number', name: '供应商商品编码', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'product_number', name: '平台商品编码', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'product_name', name: '商品名称(中)', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'product_name_en', name: '商品名称(英)', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'is_white_brand_string', name: '是否白牌', width: 68, freeze: 0, is_show: true, is_sort: false },
    { key: 'brand_name', name: '商品品牌', width: 120, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'category_string', name: '商品类目', width: 245, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'declared_purchase_tax_price', name: '申报采购单价(含税)', width: 190, freeze: 0, is_show: true, is_sort: true, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'agreed_purchase_tax_price', name: '议定采购单价(含税)', width: 190, freeze: 0, is_show: true, is_sort: true, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'selection_status_string', name: '选品状态', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'selection_notes', name: '选品意见', width: 150, freeze: 0, is_show: true, is_sort: false },
    // { key: 'selection_time', name: '选品时间', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_product_status_string', name: '状态', width: 90, freeze: 0, is_show: true, is_sort: false },
    { key: 'publish_status_string', name: '发布状态', width: 90, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '修改时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'modifier', name: '操作人', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'operate', name: '操作', width: 160, freeze: 2, is_show: true },
  ],
  [PageType.QUALIFICATION_CERTIFICATE]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'certificate_code', name: '资质/证书编码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'certificate_name', name: '资质/证书', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'manufacturer_name', name: '生产商', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'file_count', name: '资质/证书文件', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'validity_period', name: '资质有效期', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'expired_status_str', name: '过期状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'modified_at', name: '上传时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modifier_name', name: '上传人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'approval_status_str', name: '审核状态', width: 0, freeze: 0, is_show: true },
    { key: 'approval_content', name: '审核备注', width: 0, freeze: 0, is_show: true },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],
  [PageType.ORDER_MANAGE]: [
    { key: 'idx', name: '序号', width: 50, freeze: 0, is_show: true, is_sort: false, align: 'center' },
    { key: 'number', name: '采购订单编号', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'shop', name: '商品', width: 300, freeze: 0, is_show: true, is_sort: false },
    { key: 'shop_code', name: '商品编码', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'total_purchase_quantity', name: '采购数量', width: 130, freeze: 0, is_show: true, is_sort: false },
    { key: 'purchase_tax_price', name: '商品含税单价', width: 130, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'other_fees', name: '其他费用', width: 130, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'total_purchase_amount', name: '订单金额', width: 150, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'total_scheduled_quantity', name: '已发货数（已预约入库数）', width: 150, freeze: 0, is_show: true, is_sort: false, headerAlign: 'left' },
    { key: 'purchase_inbound_quantity', name: '入库数', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'predict_delivery_date', name: '协议到货日期', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'order_status', name: '订单状态', width: 110, freeze: 0, is_show: true, is_sort: false },
    { key: 'shipment_status_str', name: '发货状态', width: 110, freeze: 0, is_show: true, is_sort: false },
    { key: 'purchase_status_str', name: '收货状态', width: 110, freeze: 0, is_show: true, is_sort: false },
    { key: 'purchase', name: '采购信息', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'warehourse_name', name: '收货仓库', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 70, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.RETURN_ORDER_MANAGE]: [
    { key: 'idx', name: '序号', width: 50, freeze: 0, is_show: true, is_sort: false, align: 'center' },
    { key: 'number', name: '退库申请单编号', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'purchase_order_numbers', name: '关联采购订单编号', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'main_image', name: '商品', width: 200, freeze: 0, is_show: true, is_sort: false },
    // { key: 'sku_name', name: '退货商品', width: 200, freeze: 0, is_show: true, is_sort: false },
    // { key: 'all_category', name: '商品类目', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'product_codes', name: '商品编码', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'return_quantity', name: '退库数量', width: 100, freeze: 0, is_show: true, is_sort: false, align: 'left' },
    { key: 'return_amount', name: '退库金额', width: 120, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'other_fee_amount', name: '其他费用', width: 100, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'total_return_amount', name: '退库总金额', width: 120, freeze: 0, is_show: true, is_sort: false, align: 'right', formatter: (_) => priceFormatter(_) },
    { key: 'application_type_string', name: '申请类型', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'return_reason_type_string', name: '退货原因', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'delivery_info', name: '收货信息', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'order_status_string', name: '单据状态', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'application_info', name: '申请信息', width: 180, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 80, freeze: 2, is_show: true },
  ],
  [PageType.PRODUCT_STOCK]: [
    { key: 'main_image', name: '商品主图', width: 0, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'image', props: { fileIdKey: 'main_images_id' } } },
    { key: 'supplier_product_number', name: '供应商商品编码', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'product_number', name: '平台商品编码', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'product_name', name: '商品名称', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'category_string', name: '商品类目', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'jst_inventory_num', name: '仓库实际库存', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: (_) => defaultFormatter(_) },
    { key: 'modified_at', name: '更新时间', width: 0, freeze: 0, is_show: true, is_sort: true, formatter: (_) => defaultFormatter(_) },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],
  [PageType.SHIPORDER_MANAGE]: [
    { key: 'idx', name: '序号', width: 50, freeze: 0, is_show: true, is_sort: false, align: 'center' },
    { key: 'number', name: '发货预约单号', width: 140, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'purchase_order_numbers', name: '关联采购订单编号', width: 140, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'shop', name: '商品', width: 300, freeze: 0, is_show: true, is_sort: false },
    { key: 'shop_code', name: '商品编码', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'total_purchase_quantity', name: '采购总数', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'total_purchase_scheduled_quantity', name: '已发货数(已预约入库数)', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'scheduled_quantity', name: '本次发货数（本次预约入库数）', width: 114, freeze: 0, is_show: true, is_sort: false },
    { key: 'total_actual_inbound', name: '入库数', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'audit_status_str', name: '发货单状态', width: 200, freeze: 0, is_show: true, is_sort: false },
    {
      key: 'scheduled_arrival_time',
      name: '预计到货日期',
      width: 200,
      freeze: 0,
      is_show: true,
      is_sort: false,
      formatter: ({ cellValue }) => cellValue?.slice(0, 10),
    },
    { key: 'warehouse_name', name: '收货仓库', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'creator', name: '创建信息', width: 200, freeze: 0, is_show: true, is_sort: false },
    // { key: 'create_at', name: '创建时间', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],
}
